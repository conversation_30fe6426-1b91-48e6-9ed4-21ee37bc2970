{"name": "magicasuite", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/build.js", "build:vite": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "netlify:dev": "netlify dev"}, "dependencies": {"@netlify/functions": "^2.4.1", "@sentry/browser": "^7.80.0", "@sentry/node": "^7.91.0", "@sentry/profiling-node": "^1.2.6", "@sentry/react": "^7.80.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.0", "chart.js": "^4.4.6", "firebase": "^10.8.0", "firebase-admin": "^12.0.0", "framer-motion": "^11.11.17", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "recharts": "^2.13.3", "stripe": "^14.10.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^15.9.0", "netlify-cli": "^17.10.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "typescript-eslint": "^8.3.0", "vite": "^5.0.8"}}